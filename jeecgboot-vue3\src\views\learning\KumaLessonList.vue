<template>
  <div class="lesson-management-container">
    <!-- Course Information Header Section -->
    <div class="course-info-header">
      <a-card :bordered="false" class="course-info-card">
        <div class="course-header-content">
          <div class="course-main-info">
            <div class="course-title-section">
              <h2 class="course-title">
                <BookOutlined class="title-icon" />
                {{ courseInfo.title || '课程标题' }}
              </h2>
              <div class="course-tags">
                <a-tag v-if="courseInfo.isRecommend" color="blue">推荐</a-tag>
                <a-tag v-if="courseInfo.isHot" color="red">热门</a-tag>
                <a-tag :color="getDifficultyColor(courseInfo.level)">
                  {{ getDifficultyText(courseInfo.level) }}
                </a-tag>
              </div>
            </div>
            <p class="course-description" v-if="courseInfo.description">
              {{ courseInfo.description }}
            </p>
          </div>

          <div class="course-stats">
            <a-row :gutter="[24, 16]">
              <a-col :xs="12" :sm="6">
                <a-statistic
                  title="课时数量"
                  :value="courseStats.totalLessons"
                  suffix="节"
                  :prefix="h(PlayCircleOutlined)"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic
                  title="课程难度"
                  :value="getDifficultyText(courseInfo.level)"
                  :prefix="h(TrophyOutlined)"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic
                  title="课程价格"
                  :value="courseInfo.price || 0"
                  prefix="¥"
                  :prefix-icon="h(DollarOutlined)"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic
                  title="学习人数"
                  :value="courseInfo.studentCount || 0"
                  suffix="人"
                  :prefix="h(UserOutlined)"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>
    </div>

    <!-- Existing Lesson Management Section -->
    <div class="lesson-management-section">
      <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'learning:kuma_lesson:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'learning:kuma_lesson:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'learning:kuma_lesson:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'learning:kuma_lesson:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <!-- <template v-slot:bodyCell="{ column, record, index, text }">
      </template> -->
      
    </BasicTable>
      <!-- 表单区域 -->
      <KumaLessonModal @register="registerModal" @success="handleSuccess"></KumaLessonModal>
    </div>
  </div>
</template>

<script lang="ts" name="learning-kumaLesson" setup>
  import {ref, reactive, computed, unref, h, onMounted} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import KumaLessonModal from '/@/components/learning/KumaLessonModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './KumaLesson.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './KumaLesson.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { useRoute } from 'vue-router';
  import {defHttp} from '/@/utils/http/axios';
  import {getCourseById } from './KumaCourse.api'
  import {
    BookOutlined,
    PlayCircleOutlined,
    TrophyOutlined,
    DollarOutlined,
    UserOutlined,
    ConsoleSqlOutlined
  } from '@ant-design/icons-vue';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const route = useRoute();

  // Course Information State
  const courseInfo = reactive({
    id: '',
    title: '',
    description: '',
    level: 'beginner',
    price: 0,
    studentCount: 0,
    isRecommend: 0,
    isHot: 0
  });

  // Course Statistics
  const courseStats = reactive({
    totalLessons: 0,
    completedLessons: 0,
    publishedLessons: 0
  });
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: 'kuma_lesson',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
            afterFetch: (data) => {
              // Update course statistics when data is loaded
              updateCourseStats(data || []);
              return data;
            },
      },
       exportConfig: {
            name:"kuma_lesson",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  // Utility Functions for Course Information
  const getDifficultyColor = (level: string) => {
    const colors = {
      beginner: 'green',
      intermediate: 'orange',
      advanced: 'red'
    };
    return colors[level] || 'default';
  };

  const getDifficultyText = (level: string) => {
    const texts = {
      beginner: '初级',
      intermediate: '中级',
      advanced: '高级'
    };
    return texts[level] || level || '未设置';
  };

  // Load Course Information
  const loadCourseInfo = async () => {
    try {
      // Get course ID from route params or query
      const courseId = route.params.courseId || route.query.courseId;

      if (courseId) {

        // let courseInfo = {};
        const queryByIdUrl = '/learning/kumaCourse/queryById';
        async function initFormData(){
            let params = {id: courseId};
            const data = await defHttp.get({url: queryByIdUrl, params});
            // courseInfo = {...data}
           console.log("2@@@@@@@@@@@@@@@@@@@@"+JSON.stringify(courseInfo))
        }
          initFormData();
      } else {
        // Default course info when no specific course is selected
       
      }
    } catch (error) {
      console.error('加载课程信息失败:', error);
    }
  };

  // Update course statistics based on lesson data
  const updateCourseStats = (lessons: any[]) => {
    courseStats.totalLessons = lessons.length;
    courseStats.publishedLessons = lessons.filter(lesson => lesson.status === 1).length;
    courseStats.completedLessons = lessons.filter(lesson => lesson.isCompleted).length;
  };

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params: any) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record: any) {
     await deleteOne({id: record.id}, () => {});
     handleSuccess();
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     try {
       await batchDelete({ids: selectedRowKeys.value}, () => {});
       handleSuccess();
     } catch (error) {
       console.error('批量删除失败:', error);
     }
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
      // Update course statistics after data changes
      setTimeout(() => {
        const tableData = registerTable[1].getDataSource?.() || [];
        updateCourseStats(tableData);
      }, 100);
   }

  // Initialize course information on component mount
  onMounted(() => {
    loadCourseInfo();
  });
   /**
      * 操作栏
      */
  function getTableAction(record: any){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'learning:kuma_lesson:edit'
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record: any){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'learning:kuma_lesson:delete'
         }
       ]
   }


</script>

<style lang="less" scoped>
.lesson-management-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;

  // Course Information Header Section
  .course-info-header {
    margin-bottom: 24px;

    .course-info-card {
      border-radius: 12px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

      .course-header-content {
        .course-main-info {
          margin-bottom: 24px;

          .course-title-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .course-title {
              margin: 0;
              font-size: 24px;
              font-weight: 700;
              color: #1a1a1a;
              display: flex;
              align-items: center;
              gap: 12px;

              .title-icon {
                color: #1890ff;
                font-size: 28px;
              }
            }

            .course-tags {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;
            }
          }

          .course-description {
            margin: 0;
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            max-width: 800px;
          }
        }

        .course-stats {
          padding-top: 24px;
          border-top: 1px solid #f0f0f0;

          :deep(.ant-statistic) {
            text-align: center;

            .ant-statistic-title {
              color: #64748b;
              font-weight: 500;
              font-size: 13px;
              margin-bottom: 8px;
            }

            .ant-statistic-content {
              font-weight: 600;
              font-size: 20px;
            }
          }
        }
      }
    }
  }

  // Lesson Management Section
  .lesson-management-section {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    // Preserve existing table styles
    :deep(.ant-table) {
      .ant-table-thead > tr > th {
        background: #fafbfc;
        border-bottom: 2px solid #f0f0f0;
        font-weight: 600;
        color: #374151;
      }

      .ant-table-tbody > tr {
        &:hover > td {
          background: #f8fafc;
        }

        td {
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .lesson-management-container {
    padding: 16px;

    .course-info-header .course-info-card .course-header-content {
      .course-main-info .course-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .course-title {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .lesson-management-container {
    padding: 12px;

    .course-info-header .course-info-card .course-header-content {
      .course-stats {
        :deep(.ant-col) {
          width: 50% !important;
          max-width: none !important;
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .lesson-management-container {
    padding: 8px;

    .course-info-header .course-info-card .course-header-content {
      .course-stats {
        :deep(.ant-col) {
          width: 100% !important;
          max-width: none !important;
        }
      }
    }
  }
}

// Legacy styles for existing functionality
:deep(.ant-picker),:deep(.ant-input-number){
  width: 100%;
}
</style>