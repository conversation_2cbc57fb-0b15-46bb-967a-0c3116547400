import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '系统标识',
    align:"center",
    dataIndex: 'sysSign'
   },
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '课程ID',
    align:"center",
    dataIndex: 'courseId'
   },
   {
    title: '课时ID',
    align:"center",
    dataIndex: 'lessonId'
   },
   {
    title: '学习进度(0-100)',
    align:"center",
    dataIndex: 'progress'
   },
   {
    title: '学习时长(秒)',
    align:"center",
    dataIndex: 'duration'
   },
   {
    title: '状态：0-学习中 1-已完成',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '系统标识',
    field: 'sysSign',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入系统标识!'},
          ];
     },
  },
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入用户ID!'},
          ];
     },
  },
  {
    label: '课程ID',
    field: 'courseId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程ID!'},
          ];
     },
  },
  {
    label: '课时ID',
    field: 'lessonId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课时ID!'},
          ];
     },
  },
  {
    label: '学习进度(0-100)',
    field: 'progress',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学习进度(0-100)!'},
          ];
     },
  },
  {
    label: '学习时长(秒)',
    field: 'duration',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学习时长(秒)!'},
          ];
     },
  },
  {
    label: '状态：0-学习中 1-已完成',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入状态：0-学习中 1-已完成!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sysSign: {title: '系统标识',order: 0,view: 'text', type: 'string',},
  userId: {title: '用户ID',order: 1,view: 'text', type: 'string',},
  courseId: {title: '课程ID',order: 2,view: 'text', type: 'string',},
  lessonId: {title: '课时ID',order: 3,view: 'text', type: 'string',},
  progress: {title: '学习进度(0-100)',order: 4,view: 'number', type: 'number',},
  duration: {title: '学习时长(秒)',order: 5,view: 'number', type: 'number',},
  status: {title: '状态：0-学习中 1-已完成',order: 6,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}