import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '系统标识',
    align:"center",
    dataIndex: 'sysSign'
   },
   {
    title: '分类名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '分类图标URL',
    align:"center",
    dataIndex: 'icon'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
   {
    title: '状态：0-禁用 1-启用',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '系统标识',
    field: 'sysSign',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入系统标识!'},
          ];
     },
  },
  {
    label: '分类名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类名称!'},
          ];
     },
  },
  {
    label: '分类图标URL',
    field: 'icon',
    component: 'Input',
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入排序!'},
          ];
     },
  },
  {
    label: '状态：0-禁用 1-启用',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入状态：0-禁用 1-启用!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sysSign: {title: '系统标识',order: 0,view: 'text', type: 'string',},
  name: {title: '分类名称',order: 1,view: 'text', type: 'string',},
  icon: {title: '分类图标URL',order: 2,view: 'text', type: 'string',},
  sort: {title: '排序',order: 3,view: 'number', type: 'number',},
  status: {title: '状态：0-禁用 1-启用',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}