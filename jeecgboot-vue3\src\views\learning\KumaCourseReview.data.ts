import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '系统标识',
    align:"center",
    dataIndex: 'sysSign'
   },
   {
    title: '课程ID',
    align:"center",
    dataIndex: 'courseId'
   },
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '评分(1-5)',
    align:"center",
    dataIndex: 'rating'
   },
   {
    title: '评价内容',
    align:"center",
    dataIndex: 'content'
   },
   {
    title: '状态：0-隐藏 1-显示',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '系统标识',
    field: 'sysSign',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入系统标识!'},
          ];
     },
  },
  {
    label: '课程ID',
    field: 'courseId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程ID!'},
          ];
     },
  },
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入用户ID!'},
          ];
     },
  },
  {
    label: '评分(1-5)',
    field: 'rating',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入评分(1-5)!'},
          ];
     },
  },
  {
    label: '评价内容',
    field: 'content',
    component: 'InputTextArea',
  },
  {
    label: '状态：0-隐藏 1-显示',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入状态：0-隐藏 1-显示!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sysSign: {title: '系统标识',order: 0,view: 'text', type: 'string',},
  courseId: {title: '课程ID',order: 1,view: 'text', type: 'string',},
  userId: {title: '用户ID',order: 2,view: 'text', type: 'string',},
  rating: {title: '评分(1-5)',order: 3,view: 'number', type: 'number',},
  content: {title: '评价内容',order: 4,view: 'textarea', type: 'string',},
  status: {title: '状态：0-隐藏 1-显示',order: 5,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}